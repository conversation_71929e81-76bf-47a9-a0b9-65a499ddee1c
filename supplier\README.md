# 供应商管理模块 - 详细设计方案

## 1. 模块概述

### 1.1 模块定位
供应商管理模块是采购系统的核心基础模块，负责供应商全生命周期管理，从供应商开发、准入、评估到合作维护的完整管理体系，为采购决策提供可靠的供应商信息支持。

### 1.2 核心目标
- **供应商资源优化**：建立优质供应商资源库，提升供应商整体质量
- **风险控制**：通过评估体系和监控机制，降低供应风险
- **成本优化**：通过供应商竞争和绩效管理，实现采购成本优化
- **合作深化**：建立长期稳定的供应商合作关系

### 1.3 业务价值
- **提升采购质量**：通过严格的供应商准入和评估，确保采购商品质量
- **降低采购风险**：建立风险识别和预警机制，及时发现和处理风险
- **优化采购成本**：通过供应商竞争和绩效激励，持续优化采购成本
- **提高采购效率**：标准化的供应商管理流程，提升采购操作效率

## 2. 功能架构设计

### 2.1 功能模块结构

```
供应商管理模块
├── 供应商档案管理
│   ├── 基本信息管理
│   ├── 资质证书管理
│   ├── 联系人管理
│   └── 财务信息管理
├── 供应商准入管理
│   ├── 准入申请
│   ├── 资质审核
│   ├── 实地考察
│   └── 准入决策
├── 供应商分类管理
│   ├── 分类体系设置
│   ├── 供应商分类
│   ├── 标签管理
│   └── 等级管理
├── 供应商评估管理
│   ├── 评估体系设置
│   ├── 定期评估
│   ├── 专项评估
│   └── 评估结果管理
├── 合同协议管理
│   ├── 合同模板管理
│   ├── 合同签订
│   ├── 合同执行监控
│   └── 合同续签管理
├── 绩效分析管理
│   ├── 绩效指标设置
│   ├── 绩效数据收集
│   ├── 绩效分析报告
│   └── 改进计划跟踪
└── 供应商协同管理
    ├── 供应商门户
    ├── 信息共享
    ├── 协同作业
    └── 沟通管理
```

### 2.2 核心业务对象

#### 2.2.1 供应商档案 (Supplier)
**基本属性**：
- 供应商编码、名称、简称
- 企业类型、注册资本、成立时间
- 注册地址、办公地址、联系方式
- 法定代表人、主要联系人
- 经营范围、主营产品

**资质信息**：
- 营业执照、税务登记证
- 组织机构代码证、统一社会信用代码
- 质量管理体系认证
- 行业资质证书
- 其他相关证照

**财务信息**：
- 银行开户信息
- 税务信息
- 信用等级
- 财务状况评估

#### 2.2.2 供应商评估 (SupplierEvaluation)
**评估维度**：
- 质量管理能力
- 交付能力
- 价格竞争力
- 服务水平
- 技术创新能力
- 财务稳定性
- 合规性

**评估方式**：
- 定期评估（年度、季度、月度）
- 专项评估（新产品、重大变更）
- 实时监控（订单执行、质量反馈）

#### 2.2.3 合同协议 (SupplierContract)
**合同类型**：
- 框架协议
- 采购合同
- 服务协议
- 保密协议

**合同要素**：
- 合同条款
- 价格条件
- 交付条件
- 质量标准
- 付款条件
- 违约责任

## 3. 详细功能设计

### 3.1 供应商档案管理

#### 3.1.1 基本信息管理
**功能描述**：管理供应商的基本企业信息和经营信息

**主要功能**：
- **信息录入**：支持手动录入和批量导入
- **信息维护**：支持信息修改和历史版本管理
- **信息验证**：自动验证信息的完整性和准确性
- **信息查询**：支持多条件组合查询和模糊搜索

**业务规则**：
- 供应商编码唯一性校验
- 必填字段完整性校验
- 统一社会信用代码格式校验
- 联系方式有效性校验

#### 3.1.2 资质证书管理
**功能描述**：管理供应商的各类资质证书和认证文件

**主要功能**：
- **证书录入**：支持证书信息录入和文件上传
- **有效期管理**：自动监控证书有效期并提前预警
- **证书验证**：支持证书真伪验证和在线查询
- **证书归档**：建立完整的证书档案和历史记录

**预警机制**：
- 证书到期前30天预警
- 证书过期自动标记
- 关键证书缺失提醒
- 证书更新通知

#### 3.1.3 联系人管理
**功能描述**：管理供应商的各类联系人信息

**主要功能**：
- **联系人分类**：按职能分类管理（销售、技术、财务、质量等）
- **联系方式管理**：电话、邮箱、微信等多种联系方式
- **权限设置**：设置联系人的业务权限和访问范围
- **沟通记录**：记录与联系人的沟通历史

**联系人类型**：
- 主要联系人（业务对接）
- 技术联系人（技术支持）
- 财务联系人（财务结算）
- 质量联系人（质量管理）
- 物流联系人（物流协调）

### 3.2 供应商准入管理

#### 3.2.1 准入申请流程
**流程步骤**：
1. **申请提交**：供应商提交准入申请和相关资料
2. **资料审核**：审核申请资料的完整性和真实性
3. **初步评估**：对供应商进行初步能力评估
4. **实地考察**：必要时进行实地考察和验证
5. **综合评估**：综合各方面信息进行最终评估
6. **准入决策**：根据评估结果做出准入决策
7. **档案建立**：通过准入的供应商建立正式档案

#### 3.2.2 审核标准设置
**基础准入条件**：
- 合法经营资质
- 财务状况良好
- 质量管理体系
- 技术能力匹配
- 服务能力满足

**评估权重配置**：
- 资质条件：20%
- 财务状况：15%
- 质量能力：25%
- 技术能力：20%
- 服务能力：15%
- 价格竞争力：5%

#### 3.2.3 考察管理
**考察类型**：
- 资质考察：验证证书和资质的真实性
- 现场考察：实地查看生产和管理现状
- 能力考察：评估技术和服务能力
- 样品考察：通过样品验证产品质量

**考察记录**：
- 考察时间、地点、参与人员
- 考察内容和发现的问题
- 考察结论和改进建议
- 考察照片和相关证据

### 3.3 供应商评估管理

#### 3.3.1 评估体系设置
**评估指标体系**：

| 一级指标 | 二级指标 | 权重 | 评分标准 |
|---------|---------|------|----------|
| 质量管理 | 质量体系完善性 | 15% | 优秀(90-100)、良好(80-89)、一般(70-79)、较差(<70) |
| | 产品合格率 | 10% | >99%(100分)、95-99%(80分)、90-95%(60分)、<90%(40分) |
| 交付能力 | 交期准确率 | 15% | >95%(100分)、90-95%(80分)、85-90%(60分)、<85%(40分) |
| | 供应稳定性 | 10% | 优秀(90-100)、良好(80-89)、一般(70-79)、较差(<70) |
| 价格竞争力 | 价格水平 | 10% | 市场领先(100分)、市场平均(80分)、略高于市场(60分)、明显偏高(40分) |
| | 价格稳定性 | 5% | 优秀(90-100)、良好(80-89)、一般(70-79)、较差(<70) |
| 服务水平 | 响应速度 | 10% | 优秀(90-100)、良好(80-89)、一般(70-79)、较差(<70) |
| | 服务态度 | 5% | 优秀(90-100)、良好(80-89)、一般(70-79)、较差(<70) |
| 技术创新 | 技术实力 | 10% | 优秀(90-100)、良好(80-89)、一般(70-79)、较差(<70) |
| | 创新能力 | 5% | 优秀(90-100)、良好(80-89)、一般(70-79)、较差(<70) |
| 合规管理 | 合规性 | 5% | 完全合规(100分)、基本合规(80分)、部分违规(60分)、严重违规(0分) |

**等级划分**：
- A级供应商：综合得分≥90分，战略合作伙伴
- B级供应商：综合得分80-89分，核心供应商
- C级供应商：综合得分70-79分，一般供应商
- D级供应商：综合得分60-69分，观察期供应商
- E级供应商：综合得分<60分，淘汰供应商

#### 3.3.2 评估执行流程
**定期评估流程**：
1. **评估计划制定**：制定年度/季度评估计划
2. **数据收集**：收集评估所需的各项数据
3. **评估执行**：按照评估标准进行评分
4. **结果审核**：评估结果的审核和确认
5. **结果反馈**：向供应商反馈评估结果
6. **改进计划**：制定针对性的改进计划
7. **跟踪监控**：跟踪改进计划的执行情况

**专项评估触发条件**：
- 重大质量问题
- 严重交期延误
- 价格异常波动
- 服务投诉增加
- 合规性问题

### 3.4 绩效分析管理

#### 3.4.1 绩效指标监控
**关键绩效指标(KPI)**：

**质量指标**：
- 产品合格率 = (合格数量 / 总数量) × 100%
- 质量问题率 = (质量问题次数 / 总订单数) × 100%
- 客户投诉率 = (投诉次数 / 总订单数) × 100%

**交付指标**：
- 准时交付率 = (准时交付订单数 / 总订单数) × 100%
- 交期偏差 = 实际交期 - 承诺交期
- 供应及时率 = (及时供应次数 / 总供应次数) × 100%

**成本指标**：
- 价格竞争力指数 = (市场平均价格 / 供应商价格) × 100%
- 成本节约额 = 预算成本 - 实际成本
- 总拥有成本 = 采购成本 + 质量成本 + 交易成本

**服务指标**：
- 响应及时率 = (及时响应次数 / 总询问次数) × 100%
- 问题解决率 = (已解决问题数 / 总问题数) × 100%
- 服务满意度 = 满意度评分平均值

#### 3.4.2 绩效分析报告
**月度绩效报告**：
- 关键指标达成情况
- 与上月对比分析
- 存在问题识别
- 改进建议

**季度绩效报告**：
- 季度绩效趋势分析
- 与同类供应商对比
- 绩效改进效果评估
- 下季度重点关注事项

**年度绩效报告**：
- 年度绩效全面评估
- 供应商等级调整建议
- 合作关系发展规划
- 战略合作机会识别

## 4. 业务流程设计

### 4.1 供应商准入流程

```mermaid
graph TD
    A[供应商申请] --> B[资料收集]
    B --> C[资料完整性检查]
    C --> D{资料完整?}
    D -->|否| E[补充资料]
    E --> C
    D -->|是| F[资质审核]
    F --> G{资质审核通过?}
    G -->|否| H[审核不通过]
    G -->|是| I[初步评估]
    I --> J{是否需要实地考察?}
    J -->|是| K[安排实地考察]
    K --> L[考察执行]
    L --> M[考察报告]
    J -->|否| N[综合评估]
    M --> N
    N --> O{评估通过?}
    O -->|否| P[准入失败]
    O -->|是| Q[准入决策]
    Q --> R[建立供应商档案]
    R --> S[签订框架协议]
    S --> T[准入完成]
```

### 4.2 供应商评估流程

```mermaid
graph TD
    A[评估计划制定] --> B[确定评估对象]
    B --> C[数据收集准备]
    C --> D[质量数据收集]
    D --> E[交付数据收集]
    E --> F[成本数据收集]
    F --> G[服务数据收集]
    G --> H[技术数据收集]
    H --> I[合规数据收集]
    I --> J[数据汇总分析]
    J --> K[评估打分]
    K --> L[等级划分]
    L --> M[评估报告生成]
    M --> N[结果审核]
    N --> O{审核通过?}
    O -->|否| P[修正评估]
    P --> K
    O -->|是| Q[结果反馈]
    Q --> R[改进计划制定]
    R --> S[跟踪监控]
    S --> T[评估完成]
```

### 4.3 供应商绩效监控流程

```mermaid
graph TD
    A[绩效数据自动收集] --> B[数据清洗处理]
    B --> C[指标计算]
    C --> D[异常检测]
    D --> E{发现异常?}
    E -->|是| F[异常预警]
    F --> G[问题分析]
    G --> H[改进措施]
    H --> I[跟踪改进效果]
    E -->|否| J[正常监控]
    I --> J
    J --> K[定期报告生成]
    K --> L[绩效趋势分析]
    L --> M[管理决策支持]
```

### 4.4 供应商协同管理流程

```mermaid
graph TD
    A[协同需求产生] --> B[确定协同内容]
    B --> C[选择协同方式]
    C --> D[信息共享]
    D --> E[协同作业执行]
    E --> F[进度跟踪]
    F --> G[问题处理]
    G --> H[结果确认]
    H --> I[协同效果评估]
    I --> J[经验总结]
    J --> K[流程优化]
```

## 5. 数据模型设计

### 5.1 核心实体关系图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   供应商档案     │    │   供应商分类     │    │   供应商标签     │
│   Supplier      │────│ SupplierCategory│────│  SupplierTag    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │
         │                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   联系人信息     │    │   资质证书       │    │   财务信息       │
│ SupplierContact │    │SupplierCertificate│  │SupplierFinance  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │
         └────────────┬───────────┴────────────┬───────────┘
                      │                        │
            ┌─────────────────┐    ┌─────────────────┐
            │   供应商评估     │    │   绩效记录       │
            │SupplierEvaluation│   │PerformanceRecord│
            └─────────────────┘    └─────────────────┘
                      │                        │
                      └────────────┬───────────┘
                                   │
                      ┌─────────────────┐
                      │   合同协议       │
                      │SupplierContract │
                      └─────────────────┘
```

### 5.2 主要实体设计

#### 5.2.1 供应商档案表 (suppliers)
```sql
CREATE TABLE suppliers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '供应商ID',
    supplier_code VARCHAR(50) UNIQUE NOT NULL COMMENT '供应商编码',
    name VARCHAR(200) NOT NULL COMMENT '供应商名称',
    short_name VARCHAR(100) COMMENT '简称',
    company_type ENUM('ENTERPRISE', 'INDIVIDUAL', 'PARTNERSHIP', 'OTHER') COMMENT '企业类型',
    registered_capital DECIMAL(15,2) COMMENT '注册资本',
    established_date DATE COMMENT '成立时间',
    legal_representative VARCHAR(100) COMMENT '法定代表人',
    business_scope TEXT COMMENT '经营范围',
    main_products TEXT COMMENT '主营产品',

    -- 地址信息
    registered_address TEXT COMMENT '注册地址',
    office_address TEXT COMMENT '办公地址',

    -- 联系信息
    phone VARCHAR(20) COMMENT '联系电话',
    fax VARCHAR(20) COMMENT '传真',
    email VARCHAR(100) COMMENT '邮箱',
    website VARCHAR(200) COMMENT '网站',

    -- 证照信息
    business_license VARCHAR(100) COMMENT '营业执照号',
    unified_social_credit_code VARCHAR(50) COMMENT '统一社会信用代码',
    tax_number VARCHAR(50) COMMENT '税号',

    -- 状态信息
    status ENUM('ACTIVE', 'INACTIVE', 'BLACKLIST', 'PENDING') DEFAULT 'PENDING' COMMENT '状态',
    grade ENUM('A', 'B', 'C', 'D', 'E') COMMENT '等级',
    cooperation_level ENUM('STRATEGIC', 'CORE', 'GENERAL', 'TRIAL') DEFAULT 'TRIAL' COMMENT '合作级别',

    -- 平台信息
    platform_accounts JSON COMMENT '平台账号信息',

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',

    INDEX idx_supplier_code (supplier_code),
    INDEX idx_name (name),
    INDEX idx_status (status),
    INDEX idx_grade (grade)
) COMMENT '供应商档案表';

#### 5.2.2 供应商联系人表 (supplier_contacts)
```sql
CREATE TABLE supplier_contacts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '联系人ID',
    supplier_id BIGINT NOT NULL COMMENT '供应商ID',
    name VARCHAR(100) NOT NULL COMMENT '联系人姓名',
    position VARCHAR(100) COMMENT '职位',
    department VARCHAR(100) COMMENT '部门',
    contact_type ENUM('MAIN', 'TECHNICAL', 'FINANCIAL', 'QUALITY', 'LOGISTICS', 'OTHER') COMMENT '联系人类型',

    -- 联系方式
    phone VARCHAR(20) COMMENT '电话',
    mobile VARCHAR(20) COMMENT '手机',
    email VARCHAR(100) COMMENT '邮箱',
    wechat VARCHAR(100) COMMENT '微信',
    qq VARCHAR(20) COMMENT 'QQ',

    -- 权限设置
    business_authority JSON COMMENT '业务权限',
    access_level ENUM('HIGH', 'MEDIUM', 'LOW') DEFAULT 'MEDIUM' COMMENT '访问级别',

    -- 状态信息
    is_primary BOOLEAN DEFAULT FALSE COMMENT '是否主要联系人',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_contact_type (contact_type)
) COMMENT '供应商联系人表';
```

#### 5.2.3 供应商资质证书表 (supplier_certificates)
```sql
CREATE TABLE supplier_certificates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '证书ID',
    supplier_id BIGINT NOT NULL COMMENT '供应商ID',
    certificate_name VARCHAR(200) NOT NULL COMMENT '证书名称',
    certificate_type ENUM('BUSINESS_LICENSE', 'TAX_REGISTRATION', 'QUALITY_SYSTEM', 'INDUSTRY_QUALIFICATION', 'OTHER') COMMENT '证书类型',
    certificate_number VARCHAR(100) COMMENT '证书编号',
    issuing_authority VARCHAR(200) COMMENT '发证机关',
    issue_date DATE COMMENT '发证日期',
    expiry_date DATE COMMENT '有效期至',

    -- 文件信息
    file_path VARCHAR(500) COMMENT '文件路径',
    file_name VARCHAR(200) COMMENT '文件名称',
    file_size BIGINT COMMENT '文件大小',

    -- 验证信息
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
    verification_date TIMESTAMP COMMENT '验证时间',
    verification_result TEXT COMMENT '验证结果',

    -- 预警信息
    alert_days INT DEFAULT 30 COMMENT '预警天数',
    is_key_certificate BOOLEAN DEFAULT FALSE COMMENT '是否关键证书',

    -- 状态信息
    status ENUM('VALID', 'EXPIRED', 'REVOKED') DEFAULT 'VALID' COMMENT '状态',

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_certificate_type (certificate_type),
    INDEX idx_expiry_date (expiry_date)
) COMMENT '供应商资质证书表';
```

#### 5.2.4 供应商评估表 (supplier_evaluations)
```sql
CREATE TABLE supplier_evaluations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评估ID',
    supplier_id BIGINT NOT NULL COMMENT '供应商ID',
    evaluation_period VARCHAR(20) NOT NULL COMMENT '评估期间',
    evaluation_type ENUM('REGULAR', 'SPECIAL', 'ADMISSION') COMMENT '评估类型',

    -- 评估分数
    quality_score DECIMAL(5,2) COMMENT '质量得分',
    delivery_score DECIMAL(5,2) COMMENT '交付得分',
    price_score DECIMAL(5,2) COMMENT '价格得分',
    service_score DECIMAL(5,2) COMMENT '服务得分',
    technology_score DECIMAL(5,2) COMMENT '技术得分',
    compliance_score DECIMAL(5,2) COMMENT '合规得分',

    -- 综合评估
    total_score DECIMAL(5,2) NOT NULL COMMENT '总分',
    grade ENUM('A', 'B', 'C', 'D', 'E') COMMENT '等级',

    -- 评估详情
    evaluation_details JSON COMMENT '评估详情',
    strengths TEXT COMMENT '优势',
    weaknesses TEXT COMMENT '不足',
    improvement_suggestions TEXT COMMENT '改进建议',

    -- 评估人员
    evaluator_id BIGINT COMMENT '评估人ID',
    reviewer_id BIGINT COMMENT '审核人ID',

    -- 状态信息
    status ENUM('DRAFT', 'SUBMITTED', 'APPROVED', 'REJECTED') DEFAULT 'DRAFT' COMMENT '状态',

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_evaluation_period (evaluation_period),
    INDEX idx_grade (grade)
) COMMENT '供应商评估表';
```

#### 5.2.5 供应商绩效记录表 (supplier_performance_records)
```sql
CREATE TABLE supplier_performance_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '绩效记录ID',
    supplier_id BIGINT NOT NULL COMMENT '供应商ID',
    record_date DATE NOT NULL COMMENT '记录日期',
    record_type ENUM('DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY') COMMENT '记录类型',

    -- 质量指标
    quality_pass_rate DECIMAL(5,2) COMMENT '质量合格率',
    quality_issue_count INT DEFAULT 0 COMMENT '质量问题次数',
    customer_complaint_count INT DEFAULT 0 COMMENT '客户投诉次数',

    -- 交付指标
    on_time_delivery_rate DECIMAL(5,2) COMMENT '准时交付率',
    delivery_delay_days DECIMAL(5,2) COMMENT '交期延误天数',
    supply_stability_score DECIMAL(5,2) COMMENT '供应稳定性得分',

    -- 成本指标
    price_competitiveness_index DECIMAL(5,2) COMMENT '价格竞争力指数',
    cost_saving_amount DECIMAL(15,2) COMMENT '成本节约金额',
    total_cost_of_ownership DECIMAL(15,2) COMMENT '总拥有成本',

    -- 服务指标
    response_timeliness_rate DECIMAL(5,2) COMMENT '响应及时率',
    problem_resolution_rate DECIMAL(5,2) COMMENT '问题解决率',
    service_satisfaction_score DECIMAL(5,2) COMMENT '服务满意度得分',

    -- 订单统计
    total_orders INT DEFAULT 0 COMMENT '总订单数',
    completed_orders INT DEFAULT 0 COMMENT '完成订单数',
    total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总金额',

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_record_date (record_date),
    INDEX idx_record_type (record_type)
) COMMENT '供应商绩效记录表';

### 5.3 业务服务接口设计

#### 5.3.1 供应商档案服务接口
```java
/**
 * 供应商档案服务接口
 */
public interface SupplierService {

    /**
     * 创建供应商档案
     * @param supplierDTO 供应商信息
     * @return 供应商ID
     */
    Long createSupplier(SupplierDTO supplierDTO);

    /**
     * 更新供应商档案
     * @param supplierId 供应商ID
     * @param supplierDTO 供应商信息
     * @return 更新结果
     */
    Boolean updateSupplier(Long supplierId, SupplierDTO supplierDTO);

    /**
     * 删除供应商档案
     * @param supplierId 供应商ID
     * @return 删除结果
     */
    Boolean deleteSupplier(Long supplierId);

    /**
     * 查询供应商详情
     * @param supplierId 供应商ID
     * @return 供应商详情
     */
    SupplierDetailVO getSupplierDetail(Long supplierId);

    /**
     * 分页查询供应商列表
     * @param queryDTO 查询条件
     * @return 供应商列表
     */
    PageResult<SupplierVO> querySuppliers(SupplierQueryDTO queryDTO);

    /**
     * 批量导入供应商
     * @param supplierList 供应商列表
     * @return 导入结果
     */
    ImportResult batchImportSuppliers(List<SupplierDTO> supplierList);

    /**
     * 更新供应商状态
     * @param supplierId 供应商ID
     * @param status 状态
     * @return 更新结果
     */
    Boolean updateSupplierStatus(Long supplierId, SupplierStatus status);
}
```

#### 5.3.2 供应商评估服务接口
```java
/**
 * 供应商评估服务接口
 */
public interface SupplierEvaluationService {

    /**
     * 创建评估计划
     * @param planDTO 评估计划
     * @return 计划ID
     */
    Long createEvaluationPlan(EvaluationPlanDTO planDTO);

    /**
     * 执行供应商评估
     * @param evaluationDTO 评估信息
     * @return 评估ID
     */
    Long executeEvaluation(SupplierEvaluationDTO evaluationDTO);

    /**
     * 审核评估结果
     * @param evaluationId 评估ID
     * @param reviewDTO 审核信息
     * @return 审核结果
     */
    Boolean reviewEvaluation(Long evaluationId, EvaluationReviewDTO reviewDTO);

    /**
     * 查询评估历史
     * @param supplierId 供应商ID
     * @return 评估历史
     */
    List<SupplierEvaluationVO> getEvaluationHistory(Long supplierId);

    /**
     * 生成评估报告
     * @param evaluationId 评估ID
     * @return 评估报告
     */
    EvaluationReportVO generateEvaluationReport(Long evaluationId);

    /**
     * 批量评估供应商
     * @param supplierIds 供应商ID列表
     * @param period 评估期间
     * @return 评估结果
     */
    BatchEvaluationResult batchEvaluateSuppliers(List<Long> supplierIds, String period);
}
```

#### 5.3.3 供应商绩效服务接口
```java
/**
 * 供应商绩效服务接口
 */
public interface SupplierPerformanceService {

    /**
     * 记录绩效数据
     * @param performanceDTO 绩效数据
     * @return 记录结果
     */
    Boolean recordPerformance(SupplierPerformanceDTO performanceDTO);

    /**
     * 查询绩效统计
     * @param supplierId 供应商ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 绩效统计
     */
    PerformanceStatisticsVO getPerformanceStatistics(Long supplierId, LocalDate startDate, LocalDate endDate);

    /**
     * 生成绩效报告
     * @param reportDTO 报告参数
     * @return 绩效报告
     */
    PerformanceReportVO generatePerformanceReport(PerformanceReportDTO reportDTO);

    /**
     * 绩效预警检查
     * @return 预警列表
     */
    List<PerformanceAlertVO> checkPerformanceAlerts();

    /**
     * 供应商排名
     * @param rankingDTO 排名参数
     * @return 排名结果
     */
    List<SupplierRankingVO> getSupplierRanking(SupplierRankingDTO rankingDTO);
}
```

## 6. 核心业务逻辑实现

### 6.1 供应商评估算法

#### 6.1.1 综合评分计算
```java
/**
 * 供应商综合评分计算
 */
public class SupplierEvaluationCalculator {

    /**
     * 计算综合评分
     * @param evaluation 评估数据
     * @return 综合评分
     */
    public BigDecimal calculateTotalScore(SupplierEvaluation evaluation) {
        // 权重配置
        Map<String, BigDecimal> weights = getEvaluationWeights();

        BigDecimal totalScore = BigDecimal.ZERO;

        // 质量管理得分
        BigDecimal qualityScore = evaluation.getQualityScore()
            .multiply(weights.get("quality"));
        totalScore = totalScore.add(qualityScore);

        // 交付能力得分
        BigDecimal deliveryScore = evaluation.getDeliveryScore()
            .multiply(weights.get("delivery"));
        totalScore = totalScore.add(deliveryScore);

        // 价格竞争力得分
        BigDecimal priceScore = evaluation.getPriceScore()
            .multiply(weights.get("price"));
        totalScore = totalScore.add(priceScore);

        // 服务水平得分
        BigDecimal serviceScore = evaluation.getServiceScore()
            .multiply(weights.get("service"));
        totalScore = totalScore.add(serviceScore);

        // 技术创新得分
        BigDecimal technologyScore = evaluation.getTechnologyScore()
            .multiply(weights.get("technology"));
        totalScore = totalScore.add(technologyScore);

        // 合规管理得分
        BigDecimal complianceScore = evaluation.getComplianceScore()
            .multiply(weights.get("compliance"));
        totalScore = totalScore.add(complianceScore);

        return totalScore.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 根据得分确定等级
     * @param totalScore 总分
     * @return 等级
     */
    public SupplierGrade determineGrade(BigDecimal totalScore) {
        if (totalScore.compareTo(new BigDecimal("90")) >= 0) {
            return SupplierGrade.A;
        } else if (totalScore.compareTo(new BigDecimal("80")) >= 0) {
            return SupplierGrade.B;
        } else if (totalScore.compareTo(new BigDecimal("70")) >= 0) {
            return SupplierGrade.C;
        } else if (totalScore.compareTo(new BigDecimal("60")) >= 0) {
            return SupplierGrade.D;
        } else {
            return SupplierGrade.E;
        }
    }

    /**
     * 获取评估权重配置
     * @return 权重配置
     */
    private Map<String, BigDecimal> getEvaluationWeights() {
        Map<String, BigDecimal> weights = new HashMap<>();
        weights.put("quality", new BigDecimal("0.25"));
        weights.put("delivery", new BigDecimal("0.25"));
        weights.put("price", new BigDecimal("0.15"));
        weights.put("service", new BigDecimal("0.15"));
        weights.put("technology", new BigDecimal("0.15"));
        weights.put("compliance", new BigDecimal("0.05"));
        return weights;
    }
}
```

### 6.2 绩效监控算法

#### 6.2.1 绩效指标计算
```java
/**
 * 供应商绩效指标计算器
 */
public class PerformanceCalculator {

    /**
     * 计算质量合格率
     * @param supplierId 供应商ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 质量合格率
     */
    public BigDecimal calculateQualityPassRate(Long supplierId, LocalDate startDate, LocalDate endDate) {
        // 查询订单数据
        List<PurchaseOrder> orders = orderService.getOrdersBySupplierAndDateRange(supplierId, startDate, endDate);

        if (orders.isEmpty()) {
            return BigDecimal.ZERO;
        }

        long totalQuantity = orders.stream()
            .flatMap(order -> order.getItems().stream())
            .mapToLong(OrderItem::getQuantity)
            .sum();

        long qualifiedQuantity = orders.stream()
            .flatMap(order -> order.getItems().stream())
            .filter(item -> item.getQualityStatus() == QualityStatus.PASSED)
            .mapToLong(OrderItem::getQuantity)
            .sum();

        return BigDecimal.valueOf(qualifiedQuantity)
            .divide(BigDecimal.valueOf(totalQuantity), 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100));
    }

    /**
     * 计算准时交付率
     * @param supplierId 供应商ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 准时交付率
     */
    public BigDecimal calculateOnTimeDeliveryRate(Long supplierId, LocalDate startDate, LocalDate endDate) {
        List<PurchaseOrder> orders = orderService.getCompletedOrdersBySupplierAndDateRange(supplierId, startDate, endDate);

        if (orders.isEmpty()) {
            return BigDecimal.ZERO;
        }

        long totalOrders = orders.size();
        long onTimeOrders = orders.stream()
            .filter(order -> !order.getActualDeliveryDate().isAfter(order.getExpectedDeliveryDate()))
            .count();

        return BigDecimal.valueOf(onTimeOrders)
            .divide(BigDecimal.valueOf(totalOrders), 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100));
    }

    /**
     * 计算价格竞争力指数
     * @param supplierId 供应商ID
     * @param productId 产品ID
     * @return 价格竞争力指数
     */
    public BigDecimal calculatePriceCompetitivenessIndex(Long supplierId, Long productId) {
        // 获取市场平均价格
        BigDecimal marketAveragePrice = priceService.getMarketAveragePrice(productId);

        // 获取供应商价格
        BigDecimal supplierPrice = priceService.getSupplierPrice(supplierId, productId);

        if (supplierPrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return marketAveragePrice
            .divide(supplierPrice, 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100));
    }
}
```

### 6.3 预警机制实现

#### 6.3.1 证书到期预警
```java
/**
 * 证书到期预警服务
 */
@Service
public class CertificateAlertService {

    @Autowired
    private SupplierCertificateRepository certificateRepository;

    @Autowired
    private NotificationService notificationService;

    /**
     * 检查证书到期预警
     */
    @Scheduled(cron = "0 0 9 * * ?") // 每天上午9点执行
    public void checkCertificateExpiry() {
        LocalDate today = LocalDate.now();
        LocalDate alertDate = today.plusDays(30); // 30天预警期

        // 查询即将到期的证书
        List<SupplierCertificate> expiringCertificates = certificateRepository
            .findByExpiryDateBetweenAndStatus(today, alertDate, CertificateStatus.VALID);

        for (SupplierCertificate certificate : expiringCertificates) {
            sendExpiryAlert(certificate);
        }

        // 查询已过期的证书
        List<SupplierCertificate> expiredCertificates = certificateRepository
            .findByExpiryDateBeforeAndStatus(today, CertificateStatus.VALID);

        for (SupplierCertificate certificate : expiredCertificates) {
            handleExpiredCertificate(certificate);
        }
    }

    /**
     * 发送到期预警
     * @param certificate 证书信息
     */
    private void sendExpiryAlert(SupplierCertificate certificate) {
        AlertMessage message = AlertMessage.builder()
            .type(AlertType.CERTIFICATE_EXPIRY)
            .title("供应商证书即将到期")
            .content(String.format("供应商 %s 的 %s 将于 %s 到期，请及时更新",
                certificate.getSupplier().getName(),
                certificate.getCertificateName(),
                certificate.getExpiryDate()))
            .urgency(certificate.getIsKeyCertificate() ? Urgency.HIGH : Urgency.MEDIUM)
            .build();

        notificationService.sendAlert(message);
    }

    /**
     * 处理已过期证书
     * @param certificate 证书信息
     */
    private void handleExpiredCertificate(SupplierCertificate certificate) {
        // 更新证书状态为过期
        certificate.setStatus(CertificateStatus.EXPIRED);
        certificateRepository.save(certificate);

        // 如果是关键证书，更新供应商状态
        if (certificate.getIsKeyCertificate()) {
            Supplier supplier = certificate.getSupplier();
            supplier.setStatus(SupplierStatus.INACTIVE);
            supplierRepository.save(supplier);

            // 发送紧急预警
            AlertMessage message = AlertMessage.builder()
                .type(AlertType.CERTIFICATE_EXPIRED)
                .title("供应商关键证书已过期")
                .content(String.format("供应商 %s 的关键证书 %s 已过期，供应商已被暂停",
                    supplier.getName(),
                    certificate.getCertificateName()))
                .urgency(Urgency.CRITICAL)
                .build();

            notificationService.sendAlert(message);
        }
    }
}
```

## 7. 总结

### 7.1 模块特点
- **全生命周期管理**：覆盖供应商从准入到退出的完整生命周期
- **多维度评估**：建立科学的多维度评估体系，确保评估的客观性和准确性
- **智能化监控**：通过自动化的绩效监控和预警机制，提升管理效率
- **数据驱动决策**：基于大量的历史数据和实时数据，为管理决策提供支持

### 7.2 技术亮点
- **灵活的评估体系**：支持自定义评估指标和权重配置
- **实时绩效监控**：实时收集和分析供应商绩效数据
- **智能预警机制**：多种预警规则，及时发现和处理风险
- **完善的数据模型**：设计完整的数据模型，支持复杂的业务场景

### 7.3 业务价值
- **提升供应商质量**：通过严格的准入和评估机制，确保供应商质量
- **降低采购风险**：建立完善的风险识别和控制机制
- **优化采购成本**：通过供应商竞争和绩效管理，持续优化成本
- **提高管理效率**：标准化的流程和自动化的工具，提升管理效率

这个供应商管理模块设计方案提供了完整的产品功能设计、业务流程设计和代码模型设计，为后续的开发实施提供了详细的指导。
```
```