import pandas as pd
import numpy as np
from datasketch import MinHash, MinHashLSH
from Levenshtein import ratio
import time
import os
import sys

def clean_vender_name(name):
    """供应商名称清洗"""
    name = str(name)
    name = name.lower()
    
    # 保留原始名称用于展示，但使用简化版本进行相似度计算
    original = name.strip()
    cleaned = original
    
    # 可选：移除常见干扰词
    for word in ["co.", "inc.", "ltd", "llc", "&", "company", "corporation"]:
        cleaned = cleaned.replace(word, "")
    
    # 移除多余空格
    cleaned = " ".join(cleaned.split())
    return cleaned, original

def calculate_similarity(text1, text2):
    """计算Levenshtein相似度"""
    if not text1 or not text2:
        return 0.0
    return ratio(text1, text2) * 100

def process_vender_names(file_path, threshold=80.0, num_perm=128):
    """处理供应商名称相似记录检测"""
    start_time = time.time()
    
    # 读取Excel文件
    try:
        df = pd.read_excel(file_path, engine='openpyxl')
    except UnicodeDecodeError:
        df = pd.read_excel(file_path, engine='openpyxl', encoding='ISO-8859-1')
    
    # 检查列名
    if 'id' not in df.columns or 'vender_name' not in df.columns:
        print("错误：文件必须包含'id'和'vender_name'两列！")
        # 尝试自动重命名
        if len(df.columns) == 2:
            df.columns = ['id', 'vender_name']
            print("已自动将前两列重命名为'id'和'vender_name'")
        else:
            print(f"检测到列: {', '.join(df.columns)}")
            return pd.DataFrame()
    
    print(f"已加载数据: {len(df)} 条供应商记录")
    
    # 预处理供应商名称
    print("正在预处理供应商名称...")
    cleaned_results = [clean_vender_name(name) for name in df['vender_name']]
    cleaned_names, original_names = zip(*cleaned_results)
    
    # 创建MinHash和LSH索引
    lsh = MinHashLSH(threshold=threshold/100, num_perm=num_perm)
    minhashes = []
    
    print(f"正在构建相似性索引 (阈值={threshold}%)...")
    for idx, name in enumerate(cleaned_names):
        m = MinHash(num_perm=num_perm)
        # 仅处理长度足够的名称
        if len(name) >= 3:
            # 使用字符三元组
            for i in range(len(name) - 2):
                gram = name[i:i+3]
                m.update(gram.encode('utf-8'))
        minhashes.append(m)
        lsh.insert(str(idx), m)
    
    # 查找相似供应商记录
    print("正在检测相似供应商名称...")
    groups = {}
    processed_ids = set()
    
    # 从最高频名称开始处理（减少小群组数量）
    name_counts = {i: count for i, (_, count) in enumerate(zip(cleaned_names, pd.Series(cleaned_names).value_counts()))}
    sorted_indices = sorted(name_counts.keys(), key=lambda i: name_counts[i], reverse=True)
    
    for idx in sorted_indices:
        if idx in processed_ids:
            continue
        
        # 在LSH中查询相似记录
        matches = [int(m) for m in lsh.query(minhashes[idx])]
        
        # 如果没有找到匹配（除了自身），跳过
        if len(matches) <= 1:
            if idx not in processed_ids:
                groups[idx] = [idx]  # 保存为单一记录组
                processed_ids.add(idx)
            continue
        
        # 分组处理
        current_group = []
        for match_id in matches:
            if match_id not in processed_ids:
                current_group.append(match_id)
                processed_ids.add(match_id)
        
        # 如果组内有多个记录
        if len(current_group) > 1:
            # 按文本长度排序（最长的作为基准）
            current_group.sort(key=lambda x: len(cleaned_names[x]), reverse=True)
            base_id = current_group[0]  # 最长名称作为分组基准
            groups[base_id] = current_group
    
    # 创建结果DataFrame
    result_data = []
    duplicate_pairs = 0
    
    # 遍历所有分组
    for base_idx, group in groups.items():
        group_list = list(group)
        # 忽略单个记录组
        if len(group_list) < 2:
            continue
        
        base_id = df.iloc[base_idx]['id']
        base_name_orig = original_names[base_idx]
        
        # 对组内其他记录
        for match_idx in group_list[1:]:
            match_id = df.iloc[match_idx]['id']
            match_name_orig = original_names[match_idx]
            match_name_cleaned = cleaned_names[match_idx]
            
            # 计算相似度
            sim_score = calculate_similarity(cleaned_names[base_idx], match_name_cleaned)
            
            # 只记录超过阈值的相似记录
            if sim_score >= threshold:
                result_data.append({
                    '基准ID': base_id,
                    '基准供应商名称': base_name_orig,
                    '匹配ID': match_id,
                    '匹配供应商名称': match_name_orig,
                    '相似度(%)': round(sim_score, 2),
                    '基准(清洗后)': cleaned_names[base_idx],
                    '匹配(清洗后)': match_name_cleaned
                })
                duplicate_pairs += 1
    
    # 创建结果DataFrame
    if not result_data:
        print("\n未找到相似供应商记录")
        return pd.DataFrame()
    
    result_df = pd.DataFrame(result_data)
    
    # 添加统计信息
    elapsed = time.time() - start_time
    unique_vendors = len(set(cleaned_names))
    duplicates_found = len(result_df)
    
    stats = [
        f"\n{' 结果统计 ':^60}",
        f"处理文件: {os.path.basename(file_path)}",
        f"总记录数: {len(df)}",
        f"唯一供应商名称: {unique_vendors}",
        f"发现的相似记录对数: {duplicates_found}",
        f"相似度阈值: {threshold}%",
        f"处理时间: {elapsed:.2f}秒"
    ]
    
    print("\n" + "\n".join(stats))
    return result_df[['基准ID', '基准供应商名称', '匹配ID', '匹配供应商名称', '相似度(%)']]

def display_progress_bar(iteration, total, length=50):
    """显示进度条"""
    percent = f"{100 * iteration / total:.1f}"
    filled_length = int(length * iteration // total)
    bar = "█" * filled_length + "-" * (length - filled_length)
    sys.stdout.write(f"\r进度: |{bar}| {percent}%")
    sys.stdout.flush()

def main():
    print("=" * 60)
    print(f"{' 供应商名称相似记录检测工具 ':^60}")
    print("=" * 60)
    print("功能说明:")
    print("- 检测供应商名称相似度超过阈值的记录")
    print("- 自动清理公司后缀等干扰词 (Co., Inc, Ltd等)")
    print("- 保留原始ID和供应商名称\n")
    
    # 获取输入路径
    input_path = input("请输入Excel文件路径: ").strip()
    if not input_path:
        print("必须提供文件路径!")
        return
    
    if not os.path.exists(input_path):
        print(f"文件不存在: {input_path}")
        return
    
    # 获取输出路径
    default_output = os.path.splitext(input_path)[0] + "_相似供应商.xlsx"
    output_path = input(f"输出文件路径(默认: {default_output}): ").strip() or default_output
    
    # 检查文件是否可写
    try:
        with open(output_path, 'w') as f:
            pass
        os.remove(output_path)
    except:
        print(f"无法写入到: {output_path}，请检查权限")
        return
    
    # 获取阈值
    threshold_default = 80
    threshold_str = input(f"相似度阈值(%)(默认={threshold_default}): ").strip()
    if threshold_str:
        try:
            threshold = float(threshold_str)
            if threshold < 0 or threshold > 100:
                print("阈值必须在0-100之间，使用默认值")
                threshold = threshold_default
        except:
            print("无效输入，使用默认值")
            threshold = threshold_default
    else:
        threshold = threshold_default
    
    print("\n开始处理供应商记录...")
    
    try:
        result_df = process_vender_names(input_path, threshold=threshold)
        
        if not result_df.empty:
            # 保存结果到Excel
            result_df.to_excel(output_path, index=False)
            print(f"\n结果已保存至: {output_path}")
            
            # 手动Excel格式美化
            print("正在格式化Excel输出...")
            try:
                import openpyxl
                from openpyxl.utils.dataframe import dataframe_to_rows
                from openpyxl.styles import PatternFill, Font, Border, Side, Alignment
                
                wb = openpyxl.Workbook()
                ws = wb.active
                
                # 标题行样式
                header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
                header_font = Font(color="FFFFFF", bold=True)
                
                # 添加数据
                for r_idx, row in enumerate(dataframe_to_rows(result_df, index=False, header=True), 1):
                    # 处理标题行
                    if r_idx == 1:
                        header_row = row
                        for cell in ws[r_idx]:
                            cell.fill = header_fill
                            cell.font = header_font
                
                # 写入数据
                for r_idx, row in enumerate(dataframe_to_rows(result_df, index=False, header=True), 1):
                    ws.append(row)
                
                # 调整列宽
                for col in ws.columns:
                    max_length = 0
                    column = col[0].column_letter
                    for cell in col:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = (max_length + 2) * 1.2
                    ws.column_dimensions[column].width = min(adjusted_width, 50)
                
                # 添加边框
                thin_border = Border(left=Side(style='thin'), right=Side(style='thin'),
                                     top=Side(style='thin'), bottom=Side(style='thin'))
                
                for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=len(result_df.columns)):
                    for cell in row:
                        cell.border = thin_border
                        cell.alignment = Alignment(horizontal="left", vertical="center")
                
                # 保存格式化的Excel
                wb.save(output_path)
            except:
                print("未能添加格式，但数据已成功导出")
            
            # 询问是否打开结果
            open_file = input("是否要打开结果文件? [Y/n]: ").lower()
            if open_file in ['', 'y', 'yes']:
                if sys.platform.startswith('darwin'):  # MacOS
                    os.system(f'open "{output_path}"')
                elif 'win' in sys.platform:  # Windows
                    os.startfile(output_path)
                elif sys.platform.startswith('linux'):  # Linux
                    os.system(f'xdg-open "{output_path}"')
        else:
            print("\n未找到相似度超过阈值的供应商记录")
    
    except Exception as e:
        print(f"\n处理出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
