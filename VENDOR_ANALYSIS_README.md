# 供应商名称相似度分析工具

这是一个高效的Python脚本，用于分析CSV文件中供应商名称的相似度，特别适用于处理大量数据（5万行+）。

## 功能特点

- **智能编码检测**：自动检测和处理中文编码问题（支持UTF-8、GBK、GB2312等）
- **高效算法**：采用多种优化策略处理大数据量
  - 按字符串长度分组，减少不必要的比较
  - 多进程并行处理
  - 批处理机制
- **文本预处理**：自动清理和标准化供应商名称
- **进度显示**：实时显示处理进度
- **结果导出**：将结果保存为Excel文件，包含统计信息

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法一：交互式运行（推荐）

1. 运行交互式脚本：
```bash
python run_analysis.py
```

2. 按提示输入：
   - CSV文件路径（默认：example.csv）
   - 输出Excel文件路径（默认：similar_vendors_result.xlsx）
   - 相似度阈值（默认：0.8）

### 方法二：Windows批处理（最简单）

双击运行 `run_analysis.bat` 文件，会自动：
- 检查Python环境
- 安装依赖包
- 启动交互式分析工具

### 方法三：直接运行主脚本

1. 将您的CSV文件命名为 `example.csv` 或修改脚本中的 `input_file` 变量
2. 确保CSV文件包含 `vender_name` 列
3. 运行脚本：

```bash
python vendor_similarity_analyzer.py
```

### 自定义参数

您可以在脚本的 `main()` 函数中修改以下参数：

- `input_file`: 输入CSV文件路径
- `output_file`: 输出Excel文件路径  
- `similarity_threshold`: 相似度阈值（默认0.8，即80%）

### 高级配置

在创建 `VendorSimilarityAnalyzer` 实例时可以调整：

- `similarity_threshold`: 相似度阈值
- `max_length_diff`: 最大长度差异（用于优化比较范围）

## 输入文件格式

CSV文件应包含以下列：
- `id`: 记录ID
- `vender_name`: 供应商名称

示例：
```csv
id,vender_name
123456789,北京科技有限公司
987654321,北京科技有限责任公司
```

## 输出结果

脚本会生成一个Excel文件，包含两个工作表：

### 1. 相似供应商工作表
包含以下列：
- `id1`: 第一个供应商的ID
- `vendor_name1`: 第一个供应商名称
- `id2`: 第二个供应商的ID  
- `vendor_name2`: 第二个供应商名称
- `similarity`: 相似度分数（0-1）

### 2. 统计信息工作表
包含处理统计信息：
- 总记录数
- 相似对数
- 平均相似度
- 最高相似度
- 最低相似度

## 性能优化

对于大数据量处理，脚本采用了以下优化策略：

1. **长度分组**：只比较长度相近的字符串，避免无意义的比较
2. **多进程处理**：利用多核CPU并行计算
3. **批处理**：将大任务分解为小批次处理
4. **内存优化**：避免一次性加载所有比较结果到内存

## 注意事项

- 确保有足够的内存处理大文件
- 处理5万行数据大约需要几分钟到几十分钟（取决于CPU性能）
- 建议在处理前备份原始数据文件
- 如果遇到编码问题，脚本会自动尝试多种编码格式

## 故障排除

### 编码问题
如果遇到乱码，脚本会自动尝试多种中文编码格式。如果仍有问题，请确保CSV文件使用UTF-8编码保存。

### 内存不足
对于超大文件，可以调整 `batch_size` 参数来减少内存使用。

### 性能问题
可以调整 `n_processes` 参数来控制并行进程数，或者增加 `max_length_diff` 来减少比较次数。

## 技术细节

- **相似度算法**：使用Python标准库的 `difflib.SequenceMatcher`
- **文本预处理**：去除多余空格和特殊字符，保留中文字符
- **并行处理**：使用 `multiprocessing.Pool` 实现多进程并行
- **进度显示**：使用 `tqdm` 库显示处理进度
