#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应商名称相似度分析工具
用于分析CSV文件中vendor_name列的相似度，找出相似度超过80%的记录对
支持大数据量处理（5万行+）并输出到Excel文件
"""

import pandas as pd
import numpy as np
from difflib import SequenceMatcher
import multiprocessing as mp
from multiprocessing import Pool
import itertools
import re
import time
from tqdm import tqdm
import logging
from pathlib import Path
import chardet

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VendorSimilarityAnalyzer:
    """供应商名称相似度分析器"""
    
    def __init__(self, similarity_threshold=0.8, max_length_diff=10):
        """
        初始化分析器
        
        Args:
            similarity_threshold (float): 相似度阈值，默认0.8（80%）
            max_length_diff (int): 最大长度差异，用于优化比较范围
        """
        self.similarity_threshold = similarity_threshold
        self.max_length_diff = max_length_diff
        self.results = []
    
    def detect_encoding(self, file_path):
        """
        检测文件编码
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 检测到的编码格式
        """
        with open(file_path, 'rb') as f:
            raw_data = f.read()
            result = chardet.detect(raw_data)
            encoding = result['encoding']
            confidence = result['confidence']
            logger.info(f"检测到文件编码: {encoding} (置信度: {confidence:.2f})")
            return encoding
    
    def load_data(self, file_path):
        """
        加载CSV数据，自动处理编码问题
        
        Args:
            file_path (str): CSV文件路径
            
        Returns:
            pd.DataFrame: 加载的数据
        """
        encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5']
        
        # 首先尝试自动检测编码
        try:
            detected_encoding = self.detect_encoding(file_path)
            if detected_encoding:
                encodings_to_try.insert(0, detected_encoding)
        except Exception as e:
            logger.warning(f"编码检测失败: {e}")
        
        # 尝试不同编码加载文件
        for encoding in encodings_to_try:
            try:
                logger.info(f"尝试使用编码 {encoding} 加载文件...")
                df = pd.read_csv(file_path, encoding=encoding)
                logger.info(f"成功使用编码 {encoding} 加载文件，共 {len(df)} 行数据")
                return df
            except UnicodeDecodeError:
                logger.warning(f"编码 {encoding} 加载失败，尝试下一个...")
                continue
            except Exception as e:
                logger.error(f"使用编码 {encoding} 加载文件时出错: {e}")
                continue
        
        raise ValueError("无法使用任何编码成功加载文件")
    
    def preprocess_text(self, text):
        """
        文本预处理：清理和标准化vendor_name
        
        Args:
            text (str): 原始文本
            
        Returns:
            str: 预处理后的文本
        """
        if pd.isna(text) or text is None:
            return ""
        
        # 转换为字符串并去除首尾空格
        text = str(text).strip()
        
        # 去除多余的空格
        text = re.sub(r'\s+', ' ', text)
        
        # 去除常见的标点符号和特殊字符（保留中文字符）
        text = re.sub(r'[^\w\u4e00-\u9fff\s]', '', text)
        
        return text.strip()
    
    def calculate_similarity(self, text1, text2):
        """
        计算两个文本的相似度
        
        Args:
            text1 (str): 文本1
            text2 (str): 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        if not text1 or not text2:
            return 0.0
        
        # 使用SequenceMatcher计算相似度
        matcher = SequenceMatcher(None, text1, text2)
        return matcher.ratio()
    
    def compare_batch(self, batch_data):
        """
        批量比较函数，用于多进程处理
        
        Args:
            batch_data (tuple): (数据组合列表, 相似度阈值)
            
        Returns:
            list: 相似度超过阈值的记录对
        """
        combinations, threshold = batch_data
        batch_results = []
        
        for (idx1, name1), (idx2, name2) in combinations:
            similarity = self.calculate_similarity(name1, name2)
            if similarity >= threshold:
                batch_results.append({
                    'index1': idx1,
                    'vendor_name1': name1,
                    'index2': idx2,
                    'vendor_name2': name2,
                    'similarity': similarity
                })
        
        return batch_results
    
    def group_by_length(self, data):
        """
        按长度分组数据以优化比较
        
        Args:
            data (list): [(index, processed_name), ...]
            
        Returns:
            dict: {length_range: [(index, name), ...]}
        """
        groups = {}
        for idx, name in data:
            length = len(name)
            # 按长度范围分组，减少不必要的比较
            length_group = length // 5 * 5  # 每5个字符为一组
            if length_group not in groups:
                groups[length_group] = []
            groups[length_group].append((idx, name))
        
        return groups
    
    def find_similar_vendors(self, df, batch_size=1000, n_processes=None):
        """
        查找相似的供应商名称
        
        Args:
            df (pd.DataFrame): 包含vendor_name列的数据框
            batch_size (int): 批处理大小
            n_processes (int): 进程数，默认为CPU核心数
            
        Returns:
            list: 相似度超过阈值的记录对列表
        """
        if 'vender_name' not in df.columns:
            raise ValueError("数据中未找到 'vender_name' 列")
        
        # 预处理数据
        logger.info("开始预处理数据...")
        df['processed_name'] = df['vender_name'].apply(self.preprocess_text)
        
        # 过滤空值
        valid_data = [(idx, name) for idx, name in enumerate(df['processed_name']) 
                     if name and len(name) > 0]
        
        logger.info(f"有效数据条数: {len(valid_data)}")
        
        # 按长度分组优化
        length_groups = self.group_by_length(valid_data)
        logger.info(f"数据分为 {len(length_groups)} 个长度组")
        
        # 生成需要比较的组合
        all_combinations = []
        for length_group, items in length_groups.items():
            # 同组内比较
            if len(items) > 1:
                combinations = list(itertools.combinations(items, 2))
                all_combinations.extend(combinations)
            
            # 与相邻长度组比较
            for other_length in range(length_group - self.max_length_diff, 
                                    length_group + self.max_length_diff + 5, 5):
                if other_length in length_groups and other_length != length_group:
                    other_items = length_groups[other_length]
                    cross_combinations = list(itertools.product(items, other_items))
                    all_combinations.extend(cross_combinations)
        
        logger.info(f"总共需要比较 {len(all_combinations)} 对数据")
        
        # 分批处理
        if n_processes is None:
            n_processes = min(mp.cpu_count(), 8)  # 限制最大进程数
        
        logger.info(f"使用 {n_processes} 个进程进行并行处理")
        
        # 将组合分批
        batches = []
        for i in range(0, len(all_combinations), batch_size):
            batch = all_combinations[i:i + batch_size]
            batches.append((batch, self.similarity_threshold))
        
        logger.info(f"分为 {len(batches)} 个批次处理")
        
        # 多进程处理
        all_results = []
        with Pool(processes=n_processes) as pool:
            # 使用tqdm显示进度
            with tqdm(total=len(batches), desc="处理进度") as pbar:
                for result in pool.imap(self.compare_batch, batches):
                    all_results.extend(result)
                    pbar.update(1)
        
        logger.info(f"找到 {len(all_results)} 对相似的供应商名称")
        return all_results
    
    def save_results(self, results, df, output_file):
        """
        保存结果到Excel文件
        
        Args:
            results (list): 相似度分析结果
            df (pd.DataFrame): 原始数据
            output_file (str): 输出文件路径
        """
        if not results:
            logger.warning("没有找到相似度超过阈值的记录")
            return
        
        # 创建结果DataFrame
        result_df = pd.DataFrame(results)
        
        # 添加原始ID信息
        result_df['id1'] = result_df['index1'].apply(lambda x: df.iloc[x]['id'] if x < len(df) else None)
        result_df['id2'] = result_df['index2'].apply(lambda x: df.iloc[x]['id'] if x < len(df) else None)
        
        # 重新排列列顺序
        result_df = result_df[['id1', 'vendor_name1', 'id2', 'vendor_name2', 'similarity']]
        
        # 按相似度降序排列
        result_df = result_df.sort_values('similarity', ascending=False)
        
        # 保存到Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            result_df.to_excel(writer, sheet_name='相似供应商', index=False)
            
            # 添加统计信息
            stats_df = pd.DataFrame({
                '统计项': ['总记录数', '相似对数', '平均相似度', '最高相似度', '最低相似度'],
                '数值': [
                    len(df),
                    len(result_df),
                    f"{result_df['similarity'].mean():.4f}",
                    f"{result_df['similarity'].max():.4f}",
                    f"{result_df['similarity'].min():.4f}"
                ]
            })
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
        
        logger.info(f"结果已保存到: {output_file}")
        logger.info(f"找到 {len(result_df)} 对相似的供应商名称")

def main():
    """主函数"""
    # 配置参数
    input_file = "example.csv"  # 输入CSV文件路径
    output_file = "similar_vendors_result.xlsx"  # 输出Excel文件路径
    similarity_threshold = 0.8  # 相似度阈值（默认80%，可根据需要调整）
    
    try:
        # 创建分析器实例
        analyzer = VendorSimilarityAnalyzer(similarity_threshold=similarity_threshold)
        
        # 检查输入文件是否存在
        if not Path(input_file).exists():
            logger.error(f"输入文件不存在: {input_file}")
            return
        
        # 加载数据
        logger.info(f"开始加载数据文件: {input_file}")
        start_time = time.time()
        df = analyzer.load_data(input_file)
        
        # 查找相似供应商
        logger.info("开始分析相似度...")
        results = analyzer.find_similar_vendors(df)
        
        # 保存结果
        analyzer.save_results(results, df, output_file)
        
        # 计算总耗时
        total_time = time.time() - start_time
        logger.info(f"分析完成，总耗时: {total_time:.2f} 秒")
        
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        raise

if __name__ == "__main__":
    main()
