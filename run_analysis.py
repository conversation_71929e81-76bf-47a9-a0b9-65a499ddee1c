#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应商相似度分析运行脚本
简化版本，方便用户快速使用
"""

from vendor_similarity_analyzer import VendorSimilarityAnalyzer
import logging
import time
from pathlib import Path

def main():
    """主函数 - 简化版本"""
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    print("=" * 60)
    print("供应商名称相似度分析工具")
    print("=" * 60)
    
    # 用户输入参数
    input_file = input("请输入CSV文件路径 (默认: example.csv): ").strip()
    if not input_file:
        input_file = "example.csv"
    
    output_file = input("请输入输出Excel文件路径 (默认: similar_vendors_result.xlsx): ").strip()
    if not output_file:
        output_file = "similar_vendors_result.xlsx"
    
    threshold_input = input("请输入相似度阈值 (0-1之间，默认: 0.8): ").strip()
    try:
        similarity_threshold = float(threshold_input) if threshold_input else 0.8
        if not 0 <= similarity_threshold <= 1:
            raise ValueError("阈值必须在0-1之间")
    except ValueError as e:
        print(f"输入错误: {e}，使用默认值0.8")
        similarity_threshold = 0.8
    
    # 检查输入文件
    if not Path(input_file).exists():
        print(f"错误: 输入文件 '{input_file}' 不存在")
        return
    
    print(f"\n配置信息:")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"相似度阈值: {similarity_threshold}")
    print("-" * 60)
    
    try:
        # 创建分析器
        analyzer = VendorSimilarityAnalyzer(similarity_threshold=similarity_threshold)
        
        # 开始分析
        start_time = time.time()
        
        # 加载数据
        print("正在加载数据...")
        df = analyzer.load_data(input_file)
        
        # 分析相似度
        print("正在分析相似度...")
        results = analyzer.find_similar_vendors(df)
        
        # 保存结果
        print("正在保存结果...")
        analyzer.save_results(results, df, output_file)
        
        # 显示结果摘要
        total_time = time.time() - start_time
        print("\n" + "=" * 60)
        print("分析完成!")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"找到 {len(results)} 对相似的供应商名称")
        print(f"结果已保存到: {output_file}")
        
        if len(results) > 0:
            print("\n前5个最相似的结果:")
            # 按相似度排序并显示前5个
            sorted_results = sorted(results, key=lambda x: x['similarity'], reverse=True)
            for i, result in enumerate(sorted_results[:5], 1):
                print(f"{i}. 相似度: {result['similarity']:.3f}")
                print(f"   {result['vendor_name1']} <-> {result['vendor_name2']}")
        else:
            print(f"\n未找到相似度超过 {similarity_threshold} 的供应商名称")
            print("建议:")
            print("1. 降低相似度阈值")
            print("2. 检查数据质量")
            print("3. 确认vendor_name列名正确")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        logger.error(f"分析失败: {e}")

if __name__ == "__main__":
    main()
