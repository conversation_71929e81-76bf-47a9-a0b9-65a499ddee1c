@echo off
chcp 65001 >nul
echo ========================================
echo 供应商名称相似度分析工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查依赖包
echo 正在检查依赖包...
python -c "import pandas, numpy, openpyxl, tqdm, chardet" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

REM 运行分析脚本
echo 启动分析工具...
echo.
python run_analysis.py

echo.
echo 按任意键退出...
pause >nul
